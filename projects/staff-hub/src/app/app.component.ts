import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterOutlet } from '@angular/router';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatSidenavModule } from '@angular/material/sidenav';
import { MatListModule } from '@angular/material/list';
import { MatBadgeModule } from '@angular/material/badge';
import { Observable } from 'rxjs';
import { AuthService } from 'shared-hub';
import { environment } from '../environments/environment';

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [
    CommonModule,
    RouterOutlet,
    MatToolbarModule,
    MatIconModule,
    MatButtonModule,
    MatSidenavModule,
    MatListModule,
    MatBadgeModule
  ],
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss'
})
export class AppComponent implements OnInit {
  title = environment.appName;
  version = environment.version;

  private authService = inject(AuthService);

  // Authentication state
  authState$ = this.authService.authState$;

  ngOnInit(): void {
    console.log(`${this.title} v${this.version} initialized`);

    // Initialize PWA features
    this.initializePWA();
  }

  private initializePWA(): void {
    // Register service worker
    if ('serviceWorker' in navigator && environment.production) {
      navigator.serviceWorker.register('/ngsw-worker.js')
        .then(registration => {
          console.log('SW registered', registration);
        })
        .catch(error => {
          console.log('SW registration failed', error);
        });
    }

    // Handle app install prompt
    this.handleInstallPrompt();
  }

  private handleInstallPrompt(): void {
    let deferredPrompt: any;

    window.addEventListener('beforeinstallprompt', (e) => {
      e.preventDefault();
      deferredPrompt = e;
      // Show install button or banner
    });

    window.addEventListener('appinstalled', () => {
      console.log('StaffHub was installed');
      deferredPrompt = null;
    });
  }
}
