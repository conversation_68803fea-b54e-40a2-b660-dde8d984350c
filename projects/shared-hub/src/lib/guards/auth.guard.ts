import { Injectable, inject } from '@angular/core';
import { CanActivate, CanActivateChild, Router, ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';
import { Observable, of } from 'rxjs';
import { map, take, switchMap, catchError } from 'rxjs/operators';
import { AuthService } from '../services/auth.service';

@Injectable({
  providedIn: 'root'
})
export class AuthGuard implements CanActivate, CanActivateChild {
  private authService = inject(AuthService);
  private router = inject(Router);

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> {
    return this.checkAuth(route, state);
  }

  canActivateChild(
    childRoute: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> {
    return this.checkAuth(childRoute, state);
  }

  private checkAuth(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<boolean> {
    return this.authService.authState$.pipe(
      take(1),
      map(authState => {
        // Check if user is authenticated
        if (!authState.isAuthenticated) {
          this.handleUnauthenticated(state.url);
          return false;
        }

        // Check if staff profile is required and available
        const requiresStaffProfile = route.data?.['requiresStaffProfile'] ?? true;
        if (requiresStaffProfile && !authState.isStaff) {
          this.handleMissingStaffProfile();
          return false;
        }

        // Check required role if specified
        const requiredRole = route.data?.['requiredRole'];
        if (requiredRole && !authState.hasRole(requiredRole)) {
          this.handleInsufficientRole(requiredRole);
          return false;
        }

        // Check required permissions if specified
        const requiredPermissions = route.data?.['requiredPermissions'] as string[];
        if (requiredPermissions && requiredPermissions.length > 0) {
          const hasAllPermissions = requiredPermissions.every(permission => 
            authState.hasPermission(permission)
          );
          
          if (!hasAllPermissions) {
            this.handleInsufficientPermissions(requiredPermissions);
            return false;
          }
        }

        return true;
      }),
      catchError(error => {
        console.error('Auth guard error:', error);
        this.handleAuthError();
        return of(false);
      })
    );
  }

  private handleUnauthenticated(returnUrl: string): void {
    // Store the attempted URL for redirecting after login
    sessionStorage.setItem('returnUrl', returnUrl);
    
    // Redirect to login page
    this.router.navigate(['/login']);
  }

  private handleMissingStaffProfile(): void {
    // Redirect to profile setup or contact admin page
    this.router.navigate(['/profile-setup']);
  }

  private handleInsufficientRole(requiredRole: string): void {
    console.warn(`Access denied: Required role '${requiredRole}' not found`);
    this.router.navigate(['/access-denied'], {
      queryParams: { reason: 'insufficient-role', required: requiredRole }
    });
  }

  private handleInsufficientPermissions(requiredPermissions: string[]): void {
    console.warn(`Access denied: Required permissions not found:`, requiredPermissions);
    this.router.navigate(['/access-denied'], {
      queryParams: { reason: 'insufficient-permissions', required: requiredPermissions.join(',') }
    });
  }

  private handleAuthError(): void {
    console.error('Authentication error occurred');
    this.router.navigate(['/error'], {
      queryParams: { reason: 'auth-error' }
    });
  }
}

// Specific guard for PIN-based authentication (TimeHub)
@Injectable({
  providedIn: 'root'
})
export class PinAuthGuard implements CanActivate {
  private authService = inject(AuthService);
  private router = inject(Router);

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> {
    // Check if PIN authentication is valid
    // This would check for a valid PIN session
    const pinSession = sessionStorage.getItem('pinAuthSession');
    
    if (!pinSession) {
      this.router.navigate(['/pin-login']);
      return of(false);
    }

    try {
      const session = JSON.parse(pinSession);
      const now = new Date().getTime();
      const sessionExpiry = new Date(session.expiresAt).getTime();
      
      if (now > sessionExpiry) {
        sessionStorage.removeItem('pinAuthSession');
        this.router.navigate(['/pin-login']);
        return of(false);
      }
      
      return of(true);
    } catch (error) {
      console.error('PIN session validation error:', error);
      sessionStorage.removeItem('pinAuthSession');
      this.router.navigate(['/pin-login']);
      return of(false);
    }
  }
}

// Guard for checking if user is already authenticated (redirect from login pages)
@Injectable({
  providedIn: 'root'
})
export class NoAuthGuard implements CanActivate {
  private authService = inject(AuthService);
  private router = inject(Router);

  canActivate(): Observable<boolean> {
    return this.authService.authState$.pipe(
      take(1),
      map(authState => {
        if (authState.isAuthenticated) {
          // User is already authenticated, redirect to dashboard
          const returnUrl = sessionStorage.getItem('returnUrl') || '/dashboard';
          sessionStorage.removeItem('returnUrl');
          this.router.navigate([returnUrl]);
          return false;
        }
        return true;
      })
    );
  }
}

// Guard for kiosk mode (TimeHub)
@Injectable({
  providedIn: 'root'
})
export class KioskModeGuard implements CanActivate {
  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): boolean {
    // Check if the application is running in kiosk mode
    const isKioskMode = route.queryParams['kiosk'] === 'true' || 
                       sessionStorage.getItem('kioskMode') === 'true';
    
    if (isKioskMode) {
      // Set kiosk mode in session storage
      sessionStorage.setItem('kioskMode', 'true');
      
      // Apply kiosk mode styles/restrictions
      document.body.classList.add('kiosk-mode');
      
      // Disable right-click context menu
      document.addEventListener('contextmenu', (e) => e.preventDefault());
      
      // Disable F12, Ctrl+Shift+I, etc.
      document.addEventListener('keydown', (e) => {
        if (e.key === 'F12' || 
            (e.ctrlKey && e.shiftKey && e.key === 'I') ||
            (e.ctrlKey && e.shiftKey && e.key === 'C') ||
            (e.ctrlKey && e.key === 'u')) {
          e.preventDefault();
        }
      });
      
      return true;
    }
    
    return true; // Allow access even if not in kiosk mode
  }
}

// Utility function to check permissions
export function hasPermission(permissions: string[], required: string[]): boolean {
  return required.every(permission => permissions.includes(permission));
}

// Utility function to check role hierarchy
export function hasRoleOrHigher(userRole: string, requiredRole: string): boolean {
  const roleHierarchy = ['staff', 'manager', 'admin'];
  const userRoleIndex = roleHierarchy.indexOf(userRole);
  const requiredRoleIndex = roleHierarchy.indexOf(requiredRole);
  
  return userRoleIndex >= requiredRoleIndex;
}
