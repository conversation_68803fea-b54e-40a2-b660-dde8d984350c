{"name": "staffmanager-web", "version": "2.0.0", "description": "Advanced staff management system built with Angular 19, featuring AI-powered scheduling, comprehensive time management, and multi-business support", "keywords": ["staff-management", "scheduling", "time-tracking", "angular", "firebase", "ai-powered", "pwa", "material-design"], "author": "StaffManager Team", "license": "MIT", "homepage": "https://staffmanager.com", "repository": {"type": "git", "url": "https://github.com/staffmanager/staffmanager-web.git"}, "bugs": {"url": "https://github.com/staffmanager/staffmanager-web/issues"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "scripts": {"ng": "ng", "start": "ng serve --port 0", "start:dev": "ng serve --port 0 --open", "start:prod": "ng serve --port 0 --configuration production", "build": "ng build", "build:prod": "ng build --configuration production", "build:staging": "ng build --configuration staging", "watch": "ng build --watch --configuration development", "test": "ng test", "test:ci": "ng test --watch=false --browsers=ChromeHeadless --code-coverage", "test:coverage": "ng test --code-coverage --watch=false", "e2e": "ng e2e", "lint": "ng lint", "format": "prettier --write \"src/**/*.{ts,html,scss,json}\"", "format:check": "prettier --check \"src/**/*.{ts,html,scss,json}\"", "analyze": "ng build --stats-json && npx webpack-bundle-analyzer dist/staffmanager-web/stats.json", "build:ssr": "ng build staffmanager-web --ssr", "serve:ssr": "node dist/staffmanager-web-server/main.js", "serve:ssr:staffmanager-web": "node dist/staffmanager-web/server/server.mjs", "deploy:firebase": "npm run build:prod && firebase deploy", "deploy:staging": "npm run build:staging && firebase deploy --project staging", "deploy:prod": "npm run build:prod && firebase deploy --project production", "serve:ssr:staff-hub": "node dist/staff-hub/server/server.mjs", "serve:ssr:time-hub": "node dist/time-hub/server/server.mjs"}, "private": true, "dependencies": {"@angular/cdk": "^19.2.17", "@angular/common": "^19.2.0", "@angular/compiler": "^19.2.0", "@angular/core": "^19.2.0", "@angular/fire": "^19.2.0", "@angular/forms": "^19.2.0", "@angular/material": "^19.2.17", "@angular/platform-browser": "^19.2.0", "@angular/platform-browser-dynamic": "^19.2.0", "@angular/platform-server": "^19.2.0", "@angular/router": "^19.2.0", "@angular/service-worker": "^19.2.0", "@angular/ssr": "^19.2.13", "@auth0/auth0-angular": "^2.2.3", "@fullcalendar/angular": "^6.1.17", "@fullcalendar/core": "^6.1.17", "@fullcalendar/daygrid": "^6.1.17", "@fullcalendar/interaction": "^6.1.17", "@fullcalendar/list": "^6.1.17", "@fullcalendar/timegrid": "^6.1.17", "@google/generative-ai": "^0.24.1", "@supabase/supabase-js": "^2.49.8", "express": "^4.18.2", "file-saver": "^2.0.5", "firebase": "^11.8.1", "openai": "^4.103.0", "rxjs": "~7.8.0", "tslib": "^2.3.0", "xlsx": "^0.18.5", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.2.13", "@angular/build": "^19.2.13", "@angular/cli": "^19.2.13", "@angular/compiler-cli": "^19.2.0", "@angular/ssr": "^19.2.13", "@types/express": "^4.17.17", "@types/jasmine": "~5.1.0", "@types/node": "^18.18.0", "jasmine-core": "~5.6.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "ng-packagr": "^19.2.0", "typescript": "~5.7.2"}}